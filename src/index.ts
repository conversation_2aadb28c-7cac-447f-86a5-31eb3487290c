#!/usr/bin/env node

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
import fetch from 'node-fetch';

/**
 * Cocos Creator节点创建器
 * 专门用于与mcp-bridge.ts通信创建节点
 */
class CocosNodeCreator {
	private server: McpServer;
	private bridgeUrl: string = 'http://localhost:3001';

	constructor() {
		this.server = new McpServer({
			name: 'cocos-node-creator',
			version: '1.0.0',
		});

		this.setupNodeCreationTools();
	}

	/**
	 * 设置节点创建工具
	 */
	private setupNodeCreationTools(): void {
		// 创建基础节点
		this.server.registerTool(
			'create-node',
			{
				title: 'Create Node in Cocos Creator',
				description: 'Create a new node in the Cocos Creator editor',
				inputSchema: {
					name: z.string().describe('Name of the node'),
					parent: z
						.string()
						.optional()
						.describe('Parent node UUID (optional, defaults to scene root)'),
					position: z
						.object({
							x: z.number().default(0),
							y: z.number().default(0),
							z: z.number().default(0),
						})
						.optional()
						.describe('Position of the node'),
					components: z
						.array(z.string())
						.optional()
						.describe('Array of component types to add'),
				},
			},
			async ({ name, parent, position, components }) => {
				try {
					const nodeData = {
						name,
						parent,
						position: position || { x: 0, y: 0, z: 0 },
						components: components || [],
					};

					const result = await this.callEditorBridge('createNode', {
						data: nodeData,
					});

					return {
						content: [
							{
								type: 'text',
								text: `Successfully created node "${name}" in Cocos Creator editor.\nNode UUID: ${
									result.data?.uuid || 'unknown'
								}\nPosition: ${JSON.stringify(
									nodeData.position
								)}\nComponents: ${nodeData.components.join(', ') || 'none'}`,
							},
						],
					};
				} catch (error) {
					return {
						content: [
							{
								type: 'text',
								text: `Failed to create node "${name}": ${
									error instanceof Error ? error.message : String(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);

		// 创建带Sprite组件的节点
		this.server.registerTool(
			'create-sprite-node',
			{
				title: 'Create Sprite Node',
				description: 'Create a node with Sprite component in Cocos Creator',
				inputSchema: {
					name: z.string().describe('Name of the sprite node'),
					parent: z.string().optional().describe('Parent node UUID'),
					position: z
						.object({
							x: z.number().default(0),
							y: z.number().default(0),
							z: z.number().default(0),
						})
						.optional(),
					spriteFrame: z
						.string()
						.optional()
						.describe('Sprite frame asset path'),
				},
			},
			async ({ name, parent, position, spriteFrame }) => {
				try {
					const nodeData = {
						name,
						parent,
						position: position || { x: 0, y: 0, z: 0 },
						components: ['cc.Sprite'],
					};

					const result = await this.callEditorBridge('createNode', {
						data: nodeData,
					});

					// 如果指定了spriteFrame，设置Sprite组件的spriteFrame属性
					if (spriteFrame && result.data?.uuid) {
						await this.callEditorBridge('setComponentProperty', {
							nodeUuid: result.data.uuid,
							componentType: 'cc.Sprite',
							property: 'spriteFrame',
							value: spriteFrame,
						});
					}

					return {
						content: [
							{
								type: 'text',
								text: `Successfully created sprite node "${name}".\nNode UUID: ${
									result.data?.uuid || 'unknown'
								}\nSprite Frame: ${spriteFrame || 'default'}`,
							},
						],
					};
				} catch (error) {
					return {
						content: [
							{
								type: 'text',
								text: `Failed to create sprite node "${name}": ${
									error instanceof Error ? error.message : String(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);

		// 创建带Button组件的节点
		this.server.registerTool(
			'create-button-node',
			{
				title: 'Create Button Node',
				description: 'Create a node with Button component in Cocos Creator',
				inputSchema: {
					name: z.string().describe('Name of the button node'),
					parent: z.string().optional().describe('Parent node UUID'),
					position: z
						.object({
							x: z.number().default(0),
							y: z.number().default(0),
							z: z.number().default(0),
						})
						.optional(),
					text: z.string().optional().describe('Button text'),
				},
			},
			async ({ name, parent, position, text }) => {
				try {
					const nodeData = {
						name,
						parent,
						position: position || { x: 0, y: 0, z: 0 },
						components: ['cc.Button', 'cc.Sprite'],
					};

					const result = await this.callEditorBridge('createNode', {
						data: nodeData,
					});

					// 如果指定了文本，创建子节点显示文本
					if (text && result.data?.uuid) {
						const labelNodeData = {
							name: 'Label',
							parent: result.data.uuid,
							position: { x: 0, y: 0, z: 0 },
							components: ['cc.Label'],
						};

						const labelResult = await this.callEditorBridge('createNode', {
							data: labelNodeData,
						});

						if (labelResult.data?.uuid) {
							await this.callEditorBridge('setComponentProperty', {
								nodeUuid: labelResult.data.uuid,
								componentType: 'cc.Label',
								property: 'string',
								value: text,
							});
						}
					}

					return {
						content: [
							{
								type: 'text',
								text: `Successfully created button node "${name}".\nNode UUID: ${
									result.data?.uuid || 'unknown'
								}\nButton Text: ${text || 'none'}`,
							},
						],
					};
				} catch (error) {
					return {
						content: [
							{
								type: 'text',
								text: `Failed to create button node "${name}": ${
									error instanceof Error ? error.message : String(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}

	/**
	 * 调用编辑器桥接服务
	 */
	private async callEditorBridge(actionType: string, data: any): Promise<any> {
		try {
			const response = await fetch(`${this.bridgeUrl}/editor-action`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					type: actionType,
					...data,
				}),
			});

			if (!response.ok) {
				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			}

			const result = (await response.json()) as any;

			if (!result.success) {
				throw new Error(result.error || 'Unknown error from editor bridge');
			}

			return result;
		} catch (error) {
			if (error instanceof Error && error.message.includes('ECONNREFUSED')) {
				throw new Error(
					'Cannot connect to Cocos Creator editor. Make sure the cocos-mcp extension is installed and running.'
				);
			}
			throw error;
		}
	}

	/**
	 * 启动stdio传输
	 */
	async start(): Promise<void> {
		const transport = new StdioServerTransport();
		await this.server.connect(transport);
		console.error('Cocos Node Creator running on stdio');
	}
}

/**
 * 主函数
 */
async function main() {
	const nodeCreator = new CocosNodeCreator();

	try {
		await nodeCreator.start();
	} catch (error) {
		console.error('Failed to start node creator:', error);
		process.exit(1);
	}
}

// 如果直接运行此文件，启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
	main().catch(console.error);
}

export { CocosNodeCreator };
