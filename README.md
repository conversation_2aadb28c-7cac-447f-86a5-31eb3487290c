# Cocos Creator Node Creator

专门用于与 Cocos Creator 编辑器通信创建节点的 MCP 服务器。

## 功能特性

这个简化版本专注于节点创建功能，提供以下工具：

### 1. create-node
创建基础节点
- **参数**:
  - `name`: 节点名称 (必需)
  - `parent`: 父节点 UUID (可选，默认为场景根节点)
  - `position`: 节点位置 `{x, y, z}` (可选，默认为 `{0, 0, 0}`)
  - `components`: 组件类型数组 (可选)

### 2. create-sprite-node
创建带 Sprite 组件的节点
- **参数**:
  - `name`: 节点名称 (必需)
  - `parent`: 父节点 UUID (可选)
  - `position`: 节点位置 (可选)
  - `spriteFrame`: 精灵帧资源路径 (可选)

### 3. create-button-node
创建带 Button 组件的节点
- **参数**:
  - `name`: 节点名称 (必需)
  - `parent`: 父节点 UUID (可选)
  - `position`: 节点位置 (可选)
  - `text`: 按钮文本 (可选，会自动创建 Label 子节点)

## 前置条件

1. **Cocos Creator** 必须正在运行
2. **cocos-mcp 扩展** 必须已安装并启用
3. **mcp-bridge.ts** 必须在端口 3001 上运行

## 安装和使用

### 1. 安装依赖
```bash
npm install
```

### 2. 编译代码
```bash
npm run build
```

### 3. 启动服务器
```bash
npm start
# 或者
node dist/index.js
```

### 4. 测试连接
```bash
node test-node-creation.js
```

## 与 mcp-bridge.ts 通信

服务器通过 HTTP POST 请求与 `extensions/cocos-mcp/source/mcp-bridge.ts` 通信：

- **URL**: `http://localhost:3001/editor-action`
- **方法**: POST
- **格式**: JSON

### 请求示例
```json
{
  "type": "createNode",
  "data": {
    "name": "MyNode",
    "position": { "x": 100, "y": 200, "z": 0 },
    "components": ["cc.Sprite"]
  }
}
```

### 响应示例
```json
{
  "success": true,
  "data": {
    "uuid": "e54OU+XglDw4ZEXJM+Jfmj"
  }
}
```

## 错误处理

如果无法连接到 mcp-bridge，会显示以下错误信息：
```
Cannot connect to Cocos Creator editor. Make sure the cocos-mcp extension is installed and running.
```

## 开发说明

这是一个简化版本，专注于节点创建功能。原始的完整版本包含更多功能如资源管理、场景分析等，但为了专注于与 mcp-bridge.ts 的通信，我们移除了这些额外功能。

## 文件结构

```
├── src/
│   └── index.ts          # 主要的节点创建器代码
├── dist/                 # 编译后的 JavaScript 文件
├── test-node-creation.js # 测试脚本
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript 配置
└── README.md            # 本文档
```
