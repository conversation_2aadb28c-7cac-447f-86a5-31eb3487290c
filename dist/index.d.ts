#!/usr/bin/env node
/**
 * Cocos Creator节点创建器
 * 专门用于与mcp-bridge.ts通信创建节点
 */
declare class CocosNodeCreator {
    private server;
    private config;
    private bridgeUrl;
    constructor(configPath?: string);
    /**
     * 加载配置文件
     */
    private loadConfig;
    /**
     * 获取默认配置
     */
    private getDefaultConfig;
    /**
     * 设置节点创建工具
     */
    private setupNodeCreationTools;
    /**
     * 调用编辑器桥接服务
     */
    private callEditorBridge;
    /**
     * 启动stdio传输
     */
    start(): Promise<void>;
}
export { CocosNodeCreator };
//# sourceMappingURL=index.d.ts.map