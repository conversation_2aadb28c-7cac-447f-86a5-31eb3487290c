{"name": "cocos-mcp-server", "version": "1.0.0", "description": "MCP server for Cocos Creator node creation", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc && node dist/index.js"}, "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.17.1", "@types/node-fetch": "^2.6.13", "express": "^4.18.2", "node-fetch": "^3.3.2", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "engines": {"node": ">=18.0.0"}}