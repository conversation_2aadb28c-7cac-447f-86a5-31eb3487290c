#!/usr/bin/env node

/**
 * 测试脚本：验证与 mcp-bridge.ts 的通信
 */

import fetch from 'node-fetch';

const BRIDGE_URL = 'http://localhost:3001';

async function testBridgeConnection() {
  console.log('Testing connection to mcp-bridge...');
  
  try {
    // 测试创建节点
    const nodeData = {
      name: 'TestNode',
      position: { x: 100, y: 200, z: 0 },
      components: ['cc.Sprite']
    };

    const response = await fetch(`${BRIDGE_URL}/editor-action`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'createNode',
        data: nodeData,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Successfully connected to mcp-bridge!');
      console.log('Node creation result:', JSON.stringify(result, null, 2));
    } else {
      console.log('❌ Bridge responded but operation failed:', result.error);
    }

  } catch (error) {
    if (error.message.includes('ECONNREFUSED')) {
      console.log('❌ Cannot connect to mcp-bridge at', BRIDGE_URL);
      console.log('Make sure:');
      console.log('1. Cocos Creator is running');
      console.log('2. The cocos-mcp extension is installed and enabled');
      console.log('3. The bridge server is running on port 3001');
    } else {
      console.log('❌ Error:', error.message);
    }
  }
}

// 运行测试
testBridgeConnection();
